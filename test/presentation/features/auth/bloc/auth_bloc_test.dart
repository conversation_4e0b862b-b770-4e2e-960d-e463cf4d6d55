import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:lims_app_flutter/core/error/failures.dart';
import 'package:lims_app_flutter/core/usecases/usecase.dart';
import 'package:lims_app_flutter/domain/entities/auth/user_entity.dart';
import 'package:lims_app_flutter/domain/usecases/auth/login_usecase.dart';
import 'package:lims_app_flutter/domain/usecases/auth/logout_usecase.dart';
import 'package:lims_app_flutter/domain/usecases/auth/get_profile_usecase.dart';
import 'package:lims_app_flutter/domain/usecases/auth/update_profile_usecase.dart';
import 'package:lims_app_flutter/domain/usecases/auth/login_and_get_profile_usecase.dart';
import 'package:lims_app_flutter/presentation/features/auth/bloc/auth_bloc.dart';
import 'package:lims_app_flutter/presentation/features/auth/bloc/auth_event.dart';
import 'package:lims_app_flutter/presentation/features/auth/bloc/auth_state.dart';

import 'auth_bloc_test.mocks.dart';

@GenerateMocks([
  LoginUseCase,
  LogoutUseCase,
  GetProfileUseCase,
  UpdateProfileUseCase,
  LoginAndGetProfileUseCase,
  SharedPreferences,
])
void main() {
  late AuthBloc authBloc;
  late MockLoginUseCase mockLoginUseCase;
  late MockLogoutUseCase mockLogoutUseCase;
  late MockGetProfileUseCase mockGetProfileUseCase;
  late MockUpdateProfileUseCase mockUpdateProfileUseCase;
  late MockLoginAndGetProfileUseCase mockLoginAndGetProfileUseCase;
  late MockSharedPreferences mockSharedPreferences;

  setUp(() {
    mockLoginUseCase = MockLoginUseCase();
    mockLogoutUseCase = MockLogoutUseCase();
    mockGetProfileUseCase = MockGetProfileUseCase();
    mockUpdateProfileUseCase = MockUpdateProfileUseCase();
    mockLoginAndGetProfileUseCase = MockLoginAndGetProfileUseCase();
    mockSharedPreferences = MockSharedPreferences();

    authBloc = AuthBloc(
      loginUseCase: mockLoginUseCase,
      logoutUseCase: mockLogoutUseCase,
      getProfileUseCase: mockGetProfileUseCase,
      updateProfileUseCase: mockUpdateProfileUseCase,
      loginAndGetProfileUseCase: mockLoginAndGetProfileUseCase,
      sharedPreferences: mockSharedPreferences,
    );
  });

  tearDown(() {
    authBloc.close();
  });

  const tUser = UserEntity(
    id: 1,
    name: 'Test User',
    username: 'testuser',
    email: '<EMAIL>',
  );

  const tToken = 'test_token';

  const tLoginAndGetProfileResult = LoginAndGetProfileResult(
    token: tToken,
    user: tUser,
  );

  group('AuthBloc', () {
    test('initial state should be AuthInitial', () {
      expect(authBloc.state, equals(const AuthInitial()));
    });

    blocTest<AuthBloc, AuthState>(
      'should emit [AuthLoading, AuthAuthenticated] when login is successful',
      build: () {
        when(mockLoginAndGetProfileUseCase(any))
            .thenAnswer((_) async => const Right(tLoginAndGetProfileResult));
        return authBloc;
      },
      act: (bloc) => bloc.add(const AuthLoginRequested(
        username: 'testuser',
        password: 'password',
      )),
      expect: () => [
        const AuthLoading(),
        const AuthAuthenticated(user: tUser, token: tToken),
      ],
    );

    blocTest<AuthBloc, AuthState>(
      'should emit [AuthLoading, AuthError] when login fails',
      build: () {
        when(mockLoginAndGetProfileUseCase(any))
            .thenAnswer((_) async => const Left(ServerFailure(message: 'Login failed')));
        return authBloc;
      },
      act: (bloc) => bloc.add(const AuthLoginRequested(
        username: 'testuser',
        password: 'wrongpassword',
      )),
      expect: () => [
        const AuthLoading(),
        const AuthError(failure: ServerFailure(message: 'Login failed')),
      ],
    );

    blocTest<AuthBloc, AuthState>(
      'should emit [AuthLoading, AuthUnauthenticated] when logout is successful',
      build: () {
        when(mockLogoutUseCase(any))
            .thenAnswer((_) async => const Right(null));
        when(mockSharedPreferences.remove(any))
            .thenAnswer((_) async => true);
        return authBloc;
      },
      act: (bloc) => bloc.add(const AuthLogoutRequested()),
      expect: () => [
        const AuthLoading(),
        const AuthUnauthenticated(),
      ],
    );
  });
}
