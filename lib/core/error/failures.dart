import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  /// The error message that can be displayed to the user
  final String message;

  const Failure({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

// General failures
class ServerFailure extends Failure {
  final int? statusCode;

  const ServerFailure({
    required String message,
    this.statusCode,
  }) : super(message: message);

  @override
  List<Object> get props => [message, statusCode ?? 0];
}

class NetworkFailure extends Failure {
  final String message;

  const NetworkFailure({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

class CacheFailure extends Failure {
  final String message;

  const CacheFailure({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

class ValidationFailure extends Failure {
  final String message;
  final Map<String, List<String>>? fieldErrors;

  const ValidationFailure({
    required this.message,
    this.fieldErrors,
  });

  /// Get errors for a specific field
  List<String> getFieldErrors(String fieldName) {
    return fieldErrors?[fieldName] ?? [];
  }

  /// Get the first error for a specific field
  String? getFirstFieldError(String fieldName) {
    final errors = getFieldErrors(fieldName);
    return errors.isNotEmpty ? errors.first : null;
  }

  /// Check if there are errors for a specific field
  bool hasFieldError(String fieldName) {
    return getFieldErrors(fieldName).isNotEmpty;
  }

  /// Get all field names that have errors
  List<String> get errorFields {
    return fieldErrors?.keys.toList() ?? [];
  }

  @override
  List<Object> get props => [message, fieldErrors ?? {}];
}

class UnauthorizedFailure extends Failure {
  final String message;

  const UnauthorizedFailure({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

class GeneralFailure extends Failure {
  final String message;
  final String? details;

  const GeneralFailure({
    required this.message,
    this.details,
  });

  @override
  List<Object> get props => [message, details ?? ''];
}
