// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:lims_app_flutter/presentation/features/auth/bloc/auth_event.dart';
// import '../../features/auth/bloc/auth_bloc.dart';
// import '../../features/auth/bloc/auth_state.dart';
// import '../../features/tests/bloc/test_request_bloc.dart';
// import '../../features/tests/bloc/test_request_event.dart';
// import '../../features/tests/bloc/test_request_state.dart';
// import '../../features/jobs/bloc/job_allocation_bloc.dart';
// import '../../features/jobs/bloc/job_allocation_event.dart';
// import '../../features/jobs/bloc/job_allocation_state.dart';
// import '../../features/dashboard/bloc/dashboard_bloc.dart';
// import '../../features/dashboard/bloc/dashboard_event.dart';
// import '../../features/dashboard/bloc/dashboard_state.dart';
// import '../../features/master/bloc/master_data_bloc.dart';
// import '../../features/master/bloc/master_data_event.dart';
// import '../../features/master/bloc/master_data_state.dart';
//
// /// Examples of how to use each BLoC in the application
// class BlocUsageExamples {
//
//   /// Example: Authentication BLoC Usage
//   static Widget authenticationExample() {
//     return BlocBuilder<AuthBloc, AuthState>(
//       builder: (context, state) {
//         if (state is AuthLoading) {
//           return const CircularProgressIndicator();
//         } else if (state is AuthAuthenticated) {
//           return Column(
//             children: [
//               Text('Welcome, ${state.user.name}!'),
//               ElevatedButton(
//                 onPressed: () {
//                   context.read<AuthBloc>().add(const AuthLogoutRequested());
//                 },
//                 child: const Text('Logout'),
//               ),
//             ],
//           );
//         } else if (state is AuthError) {
//           return Column(
//             children: [
//               Text('Error: ${state.failure.message}'),
//               ElevatedButton(
//                 onPressed: () {
//                   context.read<AuthBloc>().add(const AuthClearError());
//                 },
//                 child: const Text('Clear Error'),
//               ),
//             ],
//           );
//         } else {
//           return const Text('Please login');
//         }
//       },
//     );
//   }
//
//   /// Example: Test Request BLoC Usage
//   static Widget testRequestExample() {
//     return Column(
//       children: [
//         // Load test requests button
//         ElevatedButton(
//           onPressed: () {
//             context.read<TestRequestBloc>().add(
//               const TestRequestsLoadRequested(page: 1, perPage: 10),
//             );
//           },
//           child: const Text('Load Test Requests'),
//         ),
//
//         // Test requests list
//         Expanded(
//           child: BlocBuilder<TestRequestBloc, TestRequestState>(
//             builder: (context, state) {
//               if (state is TestRequestLoading) {
//                 return const Center(child: CircularProgressIndicator());
//               } else if (state is TestRequestsLoaded) {
//                 return ListView.builder(
//                   itemCount: state.testRequests.length,
//                   itemBuilder: (context, index) {
//                     final testRequest = state.testRequests[index];
//                     return ListTile(
//                       title: Text('Test Request #${testRequest.id}'),
//                       subtitle: Text('Customer: ${testRequest.customerId}'),
//                       trailing: IconButton(
//                         icon: const Icon(Icons.delete),
//                         onPressed: () {
//                           context.read<TestRequestBloc>().add(
//                             TestRequestDeleteRequested(id: testRequest.id),
//                           );
//                         },
//                       ),
//                     );
//                   },
//                 );
//               } else if (state is TestRequestError) {
//                 return Center(
//                   child: Text('Error: ${state.failure.message}'),
//                 );
//               } else {
//                 return const Center(child: Text('No test requests loaded'));
//               }
//             },
//           ),
//         ),
//       ],
//     );
//   }
//
//   /// Example: Job Allocation BLoC Usage
//   static Widget jobAllocationExample() {
//     return Column(
//       children: [
//         // Load job allocations
//         ElevatedButton(
//           onPressed: () {
//             context.read<JobAllocationBloc>().add(
//               const JobAllocationsLoadRequested(),
//             );
//           },
//           child: const Text('Load Job Allocations'),
//         ),
//
//         // Job allocations list
//         Expanded(
//           child: BlocBuilder<JobAllocationBloc, JobAllocationState>(
//             builder: (context, state) {
//               if (state is JobAllocationLoading) {
//                 return const Center(child: CircularProgressIndicator());
//               } else if (state is JobAllocationsLoaded) {
//                 return ListView.builder(
//                   itemCount: state.jobAllocations.length,
//                   itemBuilder: (context, index) {
//                     final job = state.jobAllocations[index];
//                     return ListTile(
//                       title: Text('Job #${job.id}'),
//                       subtitle: Text('Test Request: ${job.testRequestId}'),
//                       trailing: PopupMenuButton(
//                         itemBuilder: (context) => [
//                           PopupMenuItem(
//                             child: const Text('Assign'),
//                             onTap: () {
//                               // Show analyst selection dialog
//                               context.read<JobAllocationBloc>().add(
//                                 JobAllocationAssignRequested(
//                                   jobId: job.id,
//                                   analystId: 1, // Would be selected from dialog
//                                 ),
//                               );
//                             },
//                           ),
//                           PopupMenuItem(
//                             child: const Text('Update Status'),
//                             onTap: () {
//                               context.read<JobAllocationBloc>().add(
//                                 JobAllocationStatusUpdateRequested(
//                                   jobId: job.id,
//                                   status: 'in_progress',
//                                 ),
//                               );
//                             },
//                           ),
//                         ],
//                       ),
//                     );
//                   },
//                 );
//               } else if (state is JobAllocationError) {
//                 return Center(
//                   child: Text('Error: ${state.failure.message}'),
//                 );
//               } else {
//                 return const Center(child: Text('No job allocations loaded'));
//               }
//             },
//           ),
//         ),
//       ],
//     );
//   }
//
//   /// Example: Dashboard BLoC Usage
//   static Widget dashboardExample() {
//     return Column(
//       children: [
//         // Refresh dashboard
//         ElevatedButton(
//           onPressed: () {
//             context.read<DashboardBloc>().add(
//               const DashboardStatsRefreshRequested(),
//             );
//           },
//           child: const Text('Refresh Dashboard'),
//         ),
//
//         // Dashboard content
//         Expanded(
//           child: BlocBuilder<DashboardBloc, DashboardState>(
//             builder: (context, state) {
//               if (state is DashboardLoading) {
//                 return const Center(child: CircularProgressIndicator());
//               } else if (state is DashboardStatsLoaded) {
//                 return GridView.count(
//                   crossAxisCount: 2,
//                   children: [
//                     Card(
//                       child: Padding(
//                         padding: const EdgeInsets.all(16.0),
//                         child: Column(
//                           children: [
//                             const Icon(Icons.assignment, size: 48),
//                             const SizedBox(height: 8),
//                             Text('Total Tests: ${state.stats.totalTests}'),
//                           ],
//                         ),
//                       ),
//                     ),
//                     Card(
//                       child: Padding(
//                         padding: const EdgeInsets.all(16.0),
//                         child: Column(
//                           children: [
//                             const Icon(Icons.pending, size: 48),
//                             const SizedBox(height: 8),
//                             Text('Pending: ${state.stats.pendingTests}'),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ],
//                 );
//               } else if (state is DashboardError) {
//                 return Center(
//                   child: Text('Error: ${state.failure.message}'),
//                 );
//               } else {
//                 return const Center(child: Text('Dashboard not loaded'));
//               }
//             },
//           ),
//         ),
//       ],
//     );
//   }
//
//   /// Example: Master Data BLoC Usage
//   static Widget masterDataExample() {
//     return Column(
//       children: [
//         // Load parameters and customers
//         Row(
//           children: [
//             ElevatedButton(
//               onPressed: () {
//                 context.read<MasterDataBloc>().add(
//                   const ParametersLoadRequested(),
//                 );
//               },
//               child: const Text('Load Parameters'),
//             ),
//             const SizedBox(width: 8),
//             ElevatedButton(
//               onPressed: () {
//                 context.read<MasterDataBloc>().add(
//                   const CustomersLoadRequested(),
//                 );
//               },
//               child: const Text('Load Customers'),
//             ),
//           ],
//         ),
//
//         // Master data content
//         Expanded(
//           child: BlocBuilder<MasterDataBloc, MasterDataState>(
//             builder: (context, state) {
//               if (state is MasterDataLoading) {
//                 return const Center(child: CircularProgressIndicator());
//               } else if (state is ParametersLoaded) {
//                 return ListView.builder(
//                   itemCount: state.parameters.length,
//                   itemBuilder: (context, index) {
//                     final parameter = state.parameters[index];
//                     return ListTile(
//                       title: Text(parameter.name),
//                       subtitle: Text('Type: ${parameter.type}'),
//                     );
//                   },
//                 );
//               } else if (state is CustomersLoaded) {
//                 return ListView.builder(
//                   itemCount: state.customers.length,
//                   itemBuilder: (context, index) {
//                     final customer = state.customers[index];
//                     return ListTile(
//                       title: Text(customer.name),
//                       subtitle: Text(customer.email ?? 'No email'),
//                     );
//                   },
//                 );
//               } else if (state is MasterDataError) {
//                 return Center(
//                   child: Text('Error: ${state.failure.message}'),
//                 );
//               } else {
//                 return const Center(child: Text('No master data loaded'));
//               }
//             },
//           ),
//         ),
//       ],
//     );
//   }
//
//   /// Example: Search BLoC Usage
//   static Widget searchExample() {
//     final searchController = TextEditingController();
//
//     return Column(
//       children: [
//         // Search input
//         Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: TextField(
//             controller: searchController,
//             decoration: const InputDecoration(
//               hintText: 'Search...',
//               prefixIcon: Icon(Icons.search),
//             ),
//             onSubmitted: (query) {
//               context.read<SearchBloc>().add(
//                 GlobalSearchRequested(query: query),
//               );
//             },
//           ),
//         ),
//
//         // Search results
//         Expanded(
//           child: BlocBuilder<SearchBloc, SearchState>(
//             builder: (context, state) {
//               if (state is SearchLoading) {
//                 return const Center(child: CircularProgressIndicator());
//               } else if (state is SearchResultsLoaded) {
//                 return ListView.builder(
//                   itemCount: state.searchResults.testRequests.length +
//                              state.searchResults.customers.length,
//                   itemBuilder: (context, index) {
//                     if (index < state.searchResults.testRequests.length) {
//                       final testRequest = state.searchResults.testRequests[index];
//                       return ListTile(
//                         leading: const Icon(Icons.assignment),
//                         title: Text('Test Request #${testRequest.id}'),
//                         subtitle: Text('Customer: ${testRequest.customerId}'),
//                       );
//                     } else {
//                       final customerIndex = index - state.searchResults.testRequests.length;
//                       final customer = state.searchResults.customers[customerIndex];
//                       return ListTile(
//                         leading: const Icon(Icons.person),
//                         title: Text(customer.name),
//                         subtitle: Text(customer.email ?? 'No email'),
//                       );
//                     }
//                   },
//                 );
//               } else if (state is SearchEmpty) {
//                 return Center(
//                   child: Text('No results found for "${state.query}"'),
//                 );
//               } else if (state is SearchError) {
//                 return Center(
//                   child: Text('Error: ${state.failure.message}'),
//                 );
//               } else {
//                 return const Center(child: Text('Enter a search query'));
//               }
//             },
//           ),
//         ),
//       ],
//     );
//   }
// }
