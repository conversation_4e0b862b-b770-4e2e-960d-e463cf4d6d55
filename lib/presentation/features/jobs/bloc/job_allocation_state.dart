import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../domain/entities/job_allocation/job_allocation_entity.dart';

// Simple pagination meta class for job allocations
class JobAllocationPaginationMeta extends Equatable {
  final int currentPage;
  final int totalPages;
  final int perPage;
  final int total;
  final bool hasMore;

  const JobAllocationPaginationMeta({
    required this.currentPage,
    required this.totalPages,
    required this.perPage,
    required this.total,
    required this.hasMore,
  });

  @override
  List<Object> get props => [currentPage, totalPages, perPage, total, hasMore];
}

abstract class JobAllocationState extends Equatable {
  const JobAllocationState();

  @override
  List<Object?> get props => [];
}

class JobAllocationInitial extends JobAllocationState {
  const JobAllocationInitial();
}

class JobAllocationLoading extends JobAllocationState {
  const JobAllocationLoading();
}

class JobAllocationsLoaded extends JobAllocationState {
  final List<JobAllocationEntity> jobAllocations;
  final JobAllocationPaginationMeta paginationMeta;
  final String? currentSearch;
  final String? statusFilter;
  final int? analystFilter;
  final bool isLoadingMore;

  const JobAllocationsLoaded({
    required this.jobAllocations,
    required this.paginationMeta,
    this.currentSearch,
    this.statusFilter,
    this.analystFilter,
    this.isLoadingMore = false,
  });

  @override
  List<Object?> get props => [
        jobAllocations,
        paginationMeta,
        currentSearch,
        statusFilter,
        analystFilter,
        isLoadingMore,
      ];

  JobAllocationsLoaded copyWith({
    List<JobAllocationEntity>? jobAllocations,
    JobAllocationPaginationMeta? paginationMeta,
    String? currentSearch,
    String? statusFilter,
    int? analystFilter,
    bool? isLoadingMore,
  }) {
    return JobAllocationsLoaded(
      jobAllocations: jobAllocations ?? this.jobAllocations,
      paginationMeta: paginationMeta ?? this.paginationMeta,
      currentSearch: currentSearch ?? this.currentSearch,
      statusFilter: statusFilter ?? this.statusFilter,
      analystFilter: analystFilter ?? this.analystFilter,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    );
  }
}

class JobAllocationLoaded extends JobAllocationState {
  final JobAllocationEntity jobAllocation;

  const JobAllocationLoaded({required this.jobAllocation});

  @override
  List<Object> get props => [jobAllocation];
}

class JobAllocationCreated extends JobAllocationState {
  final JobAllocationEntity jobAllocation;

  const JobAllocationCreated({required this.jobAllocation});

  @override
  List<Object> get props => [jobAllocation];
}

class JobAllocationUpdated extends JobAllocationState {
  final JobAllocationEntity jobAllocation;

  const JobAllocationUpdated({required this.jobAllocation});

  @override
  List<Object> get props => [jobAllocation];
}

class JobAllocationDeleted extends JobAllocationState {
  final int deletedId;

  const JobAllocationDeleted({required this.deletedId});

  @override
  List<Object> get props => [deletedId];
}

class JobAllocationAssigned extends JobAllocationState {
  final JobAllocationEntity jobAllocation;

  const JobAllocationAssigned({required this.jobAllocation});

  @override
  List<Object> get props => [jobAllocation];
}

class JobAllocationUnassigned extends JobAllocationState {
  final JobAllocationEntity jobAllocation;

  const JobAllocationUnassigned({required this.jobAllocation});

  @override
  List<Object> get props => [jobAllocation];
}

class JobAllocationStatusUpdated extends JobAllocationState {
  final JobAllocationEntity jobAllocation;

  const JobAllocationStatusUpdated({required this.jobAllocation});

  @override
  List<Object> get props => [jobAllocation];
}

class JobAllocationError extends JobAllocationState {
  final Failure failure;

  const JobAllocationError({required this.failure});

  @override
  List<Object> get props => [failure];
}

class JobAllocationOperationLoading extends JobAllocationState {
  final String operation;

  const JobAllocationOperationLoading({required this.operation});

  @override
  List<Object> get props => [operation];
}
