import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../domain/entities/search/global_search_result_entity.dart';

abstract class SearchState extends Equatable {
  const SearchState();

  @override
  List<Object?> get props => [];
}

class SearchInitial extends SearchState {
  const SearchInitial();
}

class SearchLoading extends SearchState {
  const SearchLoading();
}

class SearchResultsLoaded extends SearchState {
  final GlobalSearchResultEntity searchResults;
  final String query;
  final List<String>? activeModules;
  final Map<String, dynamic>? activeFilters;
  final String? sortBy;
  final String? sortOrder;

  const SearchResultsLoaded({
    required this.searchResults,
    required this.query,
    this.activeModules,
    this.activeFilters,
    this.sortBy,
    this.sortOrder,
  });

  @override
  List<Object?> get props => [
        searchResults,
        query,
        activeModules,
        activeFilters,
        sortBy,
        sortOrder,
      ];

  SearchResultsLoaded copyWith({
    GlobalSearchResultEntity? searchResults,
    String? query,
    List<String>? activeModules,
    Map<String, dynamic>? activeFilters,
    String? sortBy,
    String? sortOrder,
  }) {
    return SearchResultsLoaded(
      searchResults: searchResults ?? this.searchResults,
      query: query ?? this.query,
      activeModules: activeModules ?? this.activeModules,
      activeFilters: activeFilters ?? this.activeFilters,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }
}

class SearchSuggestionsLoaded extends SearchState {
  final List<String> suggestions;
  final String query;

  const SearchSuggestionsLoaded({
    required this.suggestions,
    required this.query,
  });

  @override
  List<Object> get props => [suggestions, query];
}

class RecentSearchesLoaded extends SearchState {
  final List<String> recentSearches;

  const RecentSearchesLoaded({required this.recentSearches});

  @override
  List<Object> get props => [recentSearches];
}

class SearchHistoryLoaded extends SearchState {
  final List<Map<String, dynamic>> searchHistory;

  const SearchHistoryLoaded({required this.searchHistory});

  @override
  List<Object> get props => [searchHistory];
}

class SearchEmpty extends SearchState {
  final String query;

  const SearchEmpty({required this.query});

  @override
  List<Object> get props => [query];
}

class SearchError extends SearchState {
  final Failure failure;

  const SearchError({required this.failure});

  @override
  List<Object> get props => [failure];
}

class SearchOperationLoading extends SearchState {
  final String operation;

  const SearchOperationLoading({required this.operation});

  @override
  List<Object> get props => [operation];
}

// Combined state for search with suggestions
class SearchWithSuggestions extends SearchState {
  final List<String> suggestions;
  final List<String> recentSearches;
  final String? currentQuery;

  const SearchWithSuggestions({
    required this.suggestions,
    required this.recentSearches,
    this.currentQuery,
  });

  @override
  List<Object?> get props => [suggestions, recentSearches, currentQuery];
}

// State for advanced search
class AdvancedSearchLoaded extends SearchState {
  final GlobalSearchResultEntity searchResults;
  final Map<String, dynamic> searchCriteria;

  const AdvancedSearchLoaded({
    required this.searchResults,
    required this.searchCriteria,
  });

  @override
  List<Object> get props => [searchResults, searchCriteria];
}
