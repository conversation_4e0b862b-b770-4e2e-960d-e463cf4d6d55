import 'package:equatable/equatable.dart';

abstract class SearchEvent extends Equatable {
  const SearchEvent();

  @override
  List<Object?> get props => [];
}

// Global search events
class GlobalSearchRequested extends SearchEvent {
  final String query;

  const GlobalSearchRequested({required this.query});

  @override
  List<Object> get props => [query];
}

class SearchCleared extends SearchEvent {
  const SearchCleared();
}

// Search with filters
class SearchWithFiltersRequested extends SearchEvent {
  final String query;
  final List<String>? modules; // e.g., ['test_requests', 'customers', 'parameters']
  final Map<String, dynamic>? filters;

  const SearchWithFiltersRequested({
    required this.query,
    this.modules,
    this.filters,
  });

  @override
  List<Object?> get props => [query, modules, filters];
}

// Search suggestions
class SearchSuggestionsRequested extends SearchEvent {
  final String query;

  const SearchSuggestionsRequested({required this.query});

  @override
  List<Object> get props => [query];
}

class SearchSuggestionsCleared extends SearchEvent {
  const SearchSuggestionsCleared();
}

// Recent searches
class RecentSearchesLoadRequested extends SearchEvent {
  const RecentSearchesLoadRequested();
}

class RecentSearchAddRequested extends SearchEvent {
  final String query;

  const RecentSearchAddRequested({required this.query});

  @override
  List<Object> get props => [query];
}

class RecentSearchClearRequested extends SearchEvent {
  const RecentSearchClearRequested();
}

class RecentSearchRemoveRequested extends SearchEvent {
  final String query;

  const RecentSearchRemoveRequested({required this.query});

  @override
  List<Object> get props => [query];
}

// Search history
class SearchHistoryLoadRequested extends SearchEvent {
  const SearchHistoryLoadRequested();
}

class SearchHistoryClearRequested extends SearchEvent {
  const SearchHistoryClearRequested();
}

// Advanced search
class AdvancedSearchRequested extends SearchEvent {
  final Map<String, dynamic> searchCriteria;

  const AdvancedSearchRequested({required this.searchCriteria});

  @override
  List<Object> get props => [searchCriteria];
}

// Search filters
class SearchFiltersUpdateRequested extends SearchEvent {
  final Map<String, dynamic> filters;

  const SearchFiltersUpdateRequested({required this.filters});

  @override
  List<Object> get props => [filters];
}

class SearchFiltersClearRequested extends SearchEvent {
  const SearchFiltersClearRequested();
}

// Search sorting
class SearchSortingUpdateRequested extends SearchEvent {
  final String sortBy;
  final String sortOrder; // 'asc' or 'desc'

  const SearchSortingUpdateRequested({
    required this.sortBy,
    required this.sortOrder,
  });

  @override
  List<Object> get props => [sortBy, sortOrder];
}

// Error handling
class SearchClearError extends SearchEvent {
  const SearchClearError();
}
