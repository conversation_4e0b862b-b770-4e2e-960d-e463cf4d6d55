import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../domain/usecases/search/global_search_usecase.dart';
import 'search_event.dart';
import 'search_state.dart';

class SearchBloc extends Bloc<SearchEvent, SearchState> {
  final GlobalSearchUseCase _globalSearchUseCase;
  final SharedPreferences _sharedPreferences;

  static const String _recentSearchesKey = 'recent_searches';
  static const String _searchHistoryKey = 'search_history';
  static const int _maxRecentSearches = 10;

  SearchBloc({
    required GlobalSearchUseCase globalSearchUseCase,
    required SharedPreferences sharedPreferences,
  })  : _globalSearchUseCase = globalSearchUseCase,
        _sharedPreferences = sharedPreferences,
        super(const SearchInitial()) {
    on<GlobalSearchRequested>(_onGlobalSearchRequested);
    on<SearchCleared>(_onSearchCleared);
    on<SearchWithFiltersRequested>(_onSearchWithFiltersRequested);
    on<SearchSuggestionsRequested>(_onSearchSuggestionsRequested);
    on<SearchSuggestionsCleared>(_onSearchSuggestionsCleared);
    on<RecentSearchesLoadRequested>(_onRecentSearchesLoadRequested);
    on<RecentSearchAddRequested>(_onRecentSearchAddRequested);
    on<RecentSearchClearRequested>(_onRecentSearchClearRequested);
    on<RecentSearchRemoveRequested>(_onRecentSearchRemoveRequested);
    on<SearchHistoryLoadRequested>(_onSearchHistoryLoadRequested);
    on<SearchHistoryClearRequested>(_onSearchHistoryClearRequested);
    on<AdvancedSearchRequested>(_onAdvancedSearchRequested);
    on<SearchFiltersUpdateRequested>(_onSearchFiltersUpdateRequested);
    on<SearchFiltersClearRequested>(_onSearchFiltersClearRequested);
    on<SearchSortingUpdateRequested>(_onSearchSortingUpdateRequested);
    on<SearchClearError>(_onSearchClearError);
  }

  Future<void> _onGlobalSearchRequested(
    GlobalSearchRequested event,
    Emitter<SearchState> emit,
  ) async {
    if (event.query.trim().isEmpty) {
      emit(const SearchInitial());
      return;
    }

    emit(const SearchLoading());

    // Add to recent searches
    await _addToRecentSearches(event.query);

    final result = await _globalSearchUseCase(
      GlobalSearchParams(query: event.query),
    );

    result.fold(
      (failure) => emit(SearchError(failure: failure)),
      (searchResults) {
        if (searchResults.isEmpty) {
          emit(SearchEmpty(query: event.query));
        } else {
          emit(SearchResultsLoaded(
            searchResults: searchResults,
            query: event.query,
          ));
        }
      },
    );
  }

  void _onSearchCleared(
    SearchCleared event,
    Emitter<SearchState> emit,
  ) {
    emit(const SearchInitial());
  }

  Future<void> _onSearchWithFiltersRequested(
    SearchWithFiltersRequested event,
    Emitter<SearchState> emit,
  ) async {
    if (event.query.trim().isEmpty) {
      emit(const SearchInitial());
      return;
    }

    emit(const SearchLoading());

    // Add to recent searches
    await _addToRecentSearches(event.query);

    // In a real implementation, you'd pass filters to the use case
    final result = await _globalSearchUseCase(
      GlobalSearchParams(query: event.query),
    );

    result.fold(
      (failure) => emit(SearchError(failure: failure)),
      (searchResults) {
        if (searchResults.isEmpty) {
          emit(SearchEmpty(query: event.query));
        } else {
          emit(SearchResultsLoaded(
            searchResults: searchResults,
            query: event.query,
            activeModules: event.modules,
            activeFilters: event.filters,
          ));
        }
      },
    );
  }

  Future<void> _onSearchSuggestionsRequested(
    SearchSuggestionsRequested event,
    Emitter<SearchState> emit,
  ) async {
    if (event.query.trim().isEmpty) {
      emit(const SearchInitial());
      return;
    }

    emit(const SearchOperationLoading(operation: 'loading_suggestions'));

    // Simulate loading suggestions
    // In a real implementation, you'd have a dedicated use case for suggestions
    await Future.delayed(const Duration(milliseconds: 300));

    final recentSearches = _getRecentSearches();
    final suggestions = recentSearches
        .where((search) => search.toLowerCase().contains(event.query.toLowerCase()))
        .take(5)
        .toList();

    // Add some mock suggestions
    suggestions.addAll([
      '${event.query} test',
      '${event.query} sample',
      '${event.query} report',
    ]);

    emit(SearchSuggestionsLoaded(
      suggestions: suggestions.take(8).toList(),
      query: event.query,
    ));
  }

  void _onSearchSuggestionsCleared(
    SearchSuggestionsCleared event,
    Emitter<SearchState> emit,
  ) {
    emit(const SearchInitial());
  }

  Future<void> _onRecentSearchesLoadRequested(
    RecentSearchesLoadRequested event,
    Emitter<SearchState> emit,
  ) async {
    final recentSearches = _getRecentSearches();
    emit(RecentSearchesLoaded(recentSearches: recentSearches));
  }

  Future<void> _onRecentSearchAddRequested(
    RecentSearchAddRequested event,
    Emitter<SearchState> emit,
  ) async {
    await _addToRecentSearches(event.query);
    final recentSearches = _getRecentSearches();
    emit(RecentSearchesLoaded(recentSearches: recentSearches));
  }

  Future<void> _onRecentSearchClearRequested(
    RecentSearchClearRequested event,
    Emitter<SearchState> emit,
  ) async {
    await _sharedPreferences.remove(_recentSearchesKey);
    emit(const RecentSearchesLoaded(recentSearches: []));
  }

  Future<void> _onRecentSearchRemoveRequested(
    RecentSearchRemoveRequested event,
    Emitter<SearchState> emit,
  ) async {
    final recentSearches = _getRecentSearches();
    recentSearches.remove(event.query);
    await _sharedPreferences.setStringList(_recentSearchesKey, recentSearches);
    emit(RecentSearchesLoaded(recentSearches: recentSearches));
  }

  Future<void> _onSearchHistoryLoadRequested(
    SearchHistoryLoadRequested event,
    Emitter<SearchState> emit,
  ) async {
    final historyJson = _sharedPreferences.getStringList(_searchHistoryKey) ?? [];
    final history = historyJson.map((json) => {
      'query': json,
      'timestamp': DateTime.now().toIso8601String(),
    }).toList();
    
    emit(SearchHistoryLoaded(searchHistory: history));
  }

  Future<void> _onSearchHistoryClearRequested(
    SearchHistoryClearRequested event,
    Emitter<SearchState> emit,
  ) async {
    await _sharedPreferences.remove(_searchHistoryKey);
    emit(const SearchHistoryLoaded(searchHistory: []));
  }

  Future<void> _onAdvancedSearchRequested(
    AdvancedSearchRequested event,
    Emitter<SearchState> emit,
  ) async {
    emit(const SearchLoading());

    // In a real implementation, you'd have a dedicated advanced search use case
    // For now, we'll use the global search with the main query from criteria
    final query = event.searchCriteria['query'] as String? ?? '';
    
    if (query.isEmpty) {
      emit(const SearchInitial());
      return;
    }

    final result = await _globalSearchUseCase(
      GlobalSearchParams(query: query),
    );

    result.fold(
      (failure) => emit(SearchError(failure: failure)),
      (searchResults) {
        if (searchResults.isEmpty) {
          emit(SearchEmpty(query: query));
        } else {
          emit(AdvancedSearchLoaded(
            searchResults: searchResults,
            searchCriteria: event.searchCriteria,
          ));
        }
      },
    );
  }

  Future<void> _onSearchFiltersUpdateRequested(
    SearchFiltersUpdateRequested event,
    Emitter<SearchState> emit,
  ) async {
    if (state is SearchResultsLoaded) {
      final currentState = state as SearchResultsLoaded;
      emit(currentState.copyWith(activeFilters: event.filters));
      
      // Re-run search with new filters
      add(SearchWithFiltersRequested(
        query: currentState.query,
        modules: currentState.activeModules,
        filters: event.filters,
      ));
    }
  }

  Future<void> _onSearchFiltersClearRequested(
    SearchFiltersClearRequested event,
    Emitter<SearchState> emit,
  ) async {
    if (state is SearchResultsLoaded) {
      final currentState = state as SearchResultsLoaded;
      emit(currentState.copyWith(
        activeFilters: null,
        activeModules: null,
      ));
      
      // Re-run search without filters
      add(GlobalSearchRequested(query: currentState.query));
    }
  }

  Future<void> _onSearchSortingUpdateRequested(
    SearchSortingUpdateRequested event,
    Emitter<SearchState> emit,
  ) async {
    if (state is SearchResultsLoaded) {
      final currentState = state as SearchResultsLoaded;
      emit(currentState.copyWith(
        sortBy: event.sortBy,
        sortOrder: event.sortOrder,
      ));
      
      // In a real implementation, you'd re-run search with new sorting
      // For now, we'll just update the state
    }
  }

  void _onSearchClearError(
    SearchClearError event,
    Emitter<SearchState> emit,
  ) {
    if (state is SearchError) {
      emit(const SearchInitial());
    }
  }

  // Helper methods for managing recent searches
  List<String> _getRecentSearches() {
    return _sharedPreferences.getStringList(_recentSearchesKey) ?? [];
  }

  Future<void> _addToRecentSearches(String query) async {
    final recentSearches = _getRecentSearches();
    
    // Remove if already exists
    recentSearches.remove(query);
    
    // Add to beginning
    recentSearches.insert(0, query);
    
    // Keep only the most recent searches
    if (recentSearches.length > _maxRecentSearches) {
      recentSearches.removeRange(_maxRecentSearches, recentSearches.length);
    }
    
    await _sharedPreferences.setStringList(_recentSearchesKey, recentSearches);
  }
}
