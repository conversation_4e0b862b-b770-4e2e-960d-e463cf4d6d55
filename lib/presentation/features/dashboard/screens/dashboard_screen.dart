import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lims_app_flutter/presentation/features/auth/bloc/auth_state.dart';
import '../../../core/router/app_routes.dart';
import '../bloc/dashboard_bloc.dart';
import '../bloc/dashboard_event.dart';
import '../bloc/dashboard_state.dart';
import '../../auth/bloc/auth_bloc.dart';
import '../../auth/bloc/auth_event.dart';

enum UserRole {
  master,
  frontDesk,
  chiefChemist,
  analyst,
}

class DashboardScreen extends StatefulWidget {
  final UserRole userRole;

  const DashboardScreen({
    super.key,
    required this.userRole,
  });

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // Load dashboard statistics when screen initializes
    context.read<DashboardBloc>().add(const DashboardStatsLoadRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('LIMS Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<DashboardBloc>().add(const DashboardStatsRefreshRequested());
            },
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              context.push(AppRoutes.notifications);
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              context.read<AuthBloc>().add(const AuthLogoutRequested());
            },
          ),
        ],
      ),
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthUnauthenticated) {
            context.go(AppRoutes.login);
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDashboardStats(),
              const SizedBox(height: 24),
              _buildRoleSpecificOptions(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDashboardStats() {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        if (state is DashboardLoading) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(32.0),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        } else if (state is DashboardStatsLoaded) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Dashboard Overview',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.5,
                children: [
                  _buildStatCard(
                    'Total',
                    state.stats.totalStats.toString(),
                    Icons.assignment,
                    Colors.blue,
                  ),
                  _buildStatCard(
                    'Monthly',
                    state.stats.monthlyStats.toString(),
                    Icons.pending,
                    Colors.orange,
                  ),
                  _buildStatCard(
                    'Tasks',
                    state.stats.taskStats.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                  _buildStatCard(
                    'Users',
                    state.stats.userStats.toString(),
                    Icons.hourglass_empty,
                    Colors.purple,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Last updated: ${_formatDateTime(state.lastUpdated)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          );
        } else if (state is DashboardError) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48,
                    color: Colors.red[300],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading dashboard',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.failure.message,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<DashboardBloc>().add(
                        const DashboardStatsLoadRequested(),
                      );
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Widget _buildRoleSpecificOptions(BuildContext context) {
    switch (widget.userRole) {
      case UserRole.master:
        return _buildMasterOptions(context);
      case UserRole.frontDesk:
        return _buildFrontDeskOptions(context);
      case UserRole.chiefChemist:
        return _buildChiefChemistOptions(context);
      case UserRole.analyst:
        return _buildAnalystOptions(context);
    }
  }

  Widget _buildMasterOptions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Master Dashboard',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        // Front Desk Section
        _buildSectionTitle('Front Desk Options'),
        _buildOptionCard(
          context,
          'View Tests',
          Icons.visibility,
          () {
            context.push(AppRoutes.viewTests);
          },
        ),
        _buildOptionCard(
          context,
          'Create Test',
          Icons.add_circle,
          () {
            context.push(AppRoutes.createTest);
          },
        ),
        _buildOptionCard(
          context,
          'View Reports',
          Icons.description,
          () {
            context.push(AppRoutes.viewReports);
          },
        ),
        const SizedBox(height: 24),

        // Chief Chemist Section
        _buildSectionTitle('Chief Chemist Options'),
        _buildOptionCard(
          context,
          'Job Allocation',
          Icons.assignment,
          () {
            context.push(AppRoutes.jobAllocation);
          },
        ),
        _buildOptionCard(
          context,
          'Approvals',
          Icons.approval,
          () {
            context.push(AppRoutes.approvals);
          },
        ),
        _buildOptionCard(
          context,
          'Quality Control',
          Icons.science,
          () {
            context.push(AppRoutes.qualityControl);
          },
        ),
        const SizedBox(height: 24),

        // Analyst Section
        _buildSectionTitle('Analyst Options'),
        _buildOptionCard(
          context,
          'View Jobs',
          Icons.work,
          () {
            context.push(AppRoutes.analystJobs);
          },
        ),
        const SizedBox(height: 24),

        // System Options
        _buildSectionTitle('System Options'),
        _buildOptionCard(
          context,
          'User Management',
          Icons.people,
          () {
            context.push(AppRoutes.userManagement);
          },
        ),
        _buildOptionCard(
          context,
          'System Settings',
          Icons.settings,
          () {
            context.push(AppRoutes.systemSettings);
          },
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildFrontDeskOptions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Front Desk Options',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildOptionCard(
          context,
          'View Tests',
          Icons.visibility,
          () {
            context.push(AppRoutes.viewTests);
          },
        ),
        _buildOptionCard(
          context,
          'Create Test',
          Icons.add_circle,
          () {
            context.push(AppRoutes.createTest);
          },
        ),
        _buildOptionCard(
          context,
          'View Reports',
          Icons.description,
          () {
            context.push(AppRoutes.viewReports);
          },
        ),
      ],
    );
  }

  Widget _buildChiefChemistOptions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Chief Chemist Options',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildOptionCard(
          context,
          'Job Allocation',
          Icons.assignment,
          () {
            context.push(AppRoutes.jobAllocation);
          },
        ),
        _buildOptionCard(
          context,
          'Approvals',
          Icons.approval,
          () {
            context.push(AppRoutes.approvals);
          },
        ),
        _buildOptionCard(
          context,
          'Quality Control',
          Icons.science,
          () {
            context.push(AppRoutes.qualityControl);
          },
        ),
        // Include Front Desk options
        const SizedBox(height: 24),
        const Text(
          'Front Desk Options',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildOptionCard(
          context,
          'View Tests',
          Icons.visibility,
          () {
            context.push(AppRoutes.viewTests);
          },
        ),
        _buildOptionCard(
          context,
          'Create Test',
          Icons.add_circle,
          () {
            context.push(AppRoutes.createTest);
          },
        ),
        _buildOptionCard(
          context,
          'View Reports',
          Icons.description,
          () {
            context.push(AppRoutes.viewReports);
          },
        ),
      ],
    );
  }

  Widget _buildAnalystOptions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Analyst Options',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildOptionCard(
          context,
          'View Jobs',
          Icons.work,
          () {
            context.push(AppRoutes.analystJobs);
          },
        ),
      ],
    );
  }

  Widget _buildOptionCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        leading: Icon(icon, size: 32),
        title: Text(
          title,
          style: const TextStyle(fontSize: 18),
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }
} 