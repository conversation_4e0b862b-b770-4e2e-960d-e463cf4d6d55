import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/job_allocation/job_allocation_entity.dart';
import '../../repositories/job_allocation_repository.dart';

class GetJobAllocationsUseCase {
  final JobAllocationRepository repository;

  GetJobAllocationsUseCase(this.repository);

  Future<Either<Failure, List<JobAllocationEntity>>> call(GetJobAllocationsParams params) async {
    return await repository.getJobAllocations();
  }
}

class GetJobAllocationsParams extends Equatable {
  final int page;
  final int perPage;
  final String? search;

  const GetJobAllocationsParams({
    this.page = 1,
    this.perPage = 15,
    this.search,
  });

  @override
  List<Object?> get props => [page, perPage, search];
}
