import 'package:equatable/equatable.dart';

class SampleEntity extends Equatable {
  final int? id;
  final int parameterId;
  final String? particulars;
  final String? type;
  final String? quantity;
  final String? remarks;
  final int? testRequestId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const SampleEntity({
    this.id,
    required this.parameterId,
     this.particulars,
     this.type,
    this.quantity,
    this.remarks,
    this.testRequestId,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        parameterId,
        particulars,
        type,
        quantity,
        remarks,
        testRequestId,
        createdAt,
        updatedAt,
      ];
}
