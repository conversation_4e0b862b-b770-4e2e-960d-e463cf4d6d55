import 'package:equatable/equatable.dart';
import 'sample_entity.dart';

class TestRequestEntity extends Equatable {
  final int id;
  final String requestNumber;
  final DateTime requestDate;
  final String customerName;
  final String customerAddress;
  final String contactPerson;
  final String mobileNo;
  final String email;
  final String? specialRequest;
  final String submittedByName;
  final String submittedByDesignation;
  final DateTime submittedByDate;
  final String submittedByIdProof;
  final String receivedByName;
  final String receivedByDesignation;
  final DateTime receivedByDate;
  final String receivedByIdProof;
  final DateTime sampleReceivedTime;
  final DateTime sampleCollectionTime;
  final String quantityOfSample;
  final String? typeOfSample;
  final String? sampleDetails;
  final String? sampleCode;
  final List<SampleEntity>? samples;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const TestRequestEntity({
    required this.id,
    required this.requestNumber,
    required this.requestDate,
    required this.customerName,
    required this.customerAddress,
    required this.contactPerson,
    required this.mobileNo,
    required this.email,
    this.specialRequest,
    required this.submittedByName,
    required this.submittedByDesignation,
    required this.submittedByDate,
    required this.submittedByIdProof,
    required this.receivedByName,
    required this.receivedByDesignation,
    required this.receivedByDate,
    required this.receivedByIdProof,
    required this.sampleReceivedTime,
    required this.sampleCollectionTime,
    required this.quantityOfSample,
    this.typeOfSample,
    this.sampleDetails,
    this.sampleCode,
    this.samples,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        requestNumber,
        requestDate,
        customerName,
        customerAddress,
        contactPerson,
        mobileNo,
        email,
        specialRequest,
        submittedByName,
        submittedByDesignation,
        submittedByDate,
        submittedByIdProof,
        receivedByName,
        receivedByDesignation,
        receivedByDate,
        receivedByIdProof,
        sampleReceivedTime,
        sampleCollectionTime,
        quantityOfSample,
        typeOfSample,
        sampleDetails,
        sampleCode,
        samples,
        createdAt,
        updatedAt,
      ];
}
