// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'test_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TestRequestModel _$TestRequestModelFromJson(Map<String, dynamic> json) =>
    TestRequestModel(
      id: (json['id'] as num).toInt(),
      requestNumber: json['request_number'] as String,
      requestDate: json['request_date'] as String,
      customerName: json['customer_name'] as String,
      customerAddress: json['customer_address'] as String,
      contactPerson: json['contact_person'] as String,
      mobileNo: json['mobile_no'] as String,
      email: json['email'] as String,
      specialRequest: json['special_request'] as String?,
      submittedByName: json['submitted_by_name'] as String,
      submittedByDesignation: json['submitted_by_designation'] as String,
      submittedByDate: json['submitted_by_date'] as String,
      submittedByIdProof: json['submitted_by_id_proof'] as String,
      receivedByName: json['received_by_name'] as String,
      receivedByDesignation: json['received_by_designation'] as String,
      receivedByDate: json['received_by_date'] as String,
      receivedByIdProof: json['received_by_id_proof'] as String,
      sampleReceivedTime: json['sample_received_time'] as String,
      sampleCollectionTime: json['sample_collection_time'] as String,
      quantityOfSample: json['quantity_of_sample'] as String,
      typeOfSample: json['type_of_sample'] as String?,
      sampleDetails: json['sample_details'] as String?,
      sampleCode: json['sample_code'] as String?,
      samples: (json['samples'] as List<dynamic>?)
          ?.map((e) => SampleModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      deleted: (json['deleted'] as num?)?.toInt() ?? 0,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$TestRequestModelToJson(TestRequestModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'request_number': instance.requestNumber,
      'request_date': instance.requestDate,
      'customer_name': instance.customerName,
      'customer_address': instance.customerAddress,
      'contact_person': instance.contactPerson,
      'mobile_no': instance.mobileNo,
      'email': instance.email,
      'special_request': instance.specialRequest,
      'submitted_by_name': instance.submittedByName,
      'submitted_by_designation': instance.submittedByDesignation,
      'submitted_by_date': instance.submittedByDate,
      'submitted_by_id_proof': instance.submittedByIdProof,
      'received_by_name': instance.receivedByName,
      'received_by_designation': instance.receivedByDesignation,
      'received_by_date': instance.receivedByDate,
      'received_by_id_proof': instance.receivedByIdProof,
      'sample_received_time': instance.sampleReceivedTime,
      'sample_collection_time': instance.sampleCollectionTime,
      'quantity_of_sample': instance.quantityOfSample,
      'type_of_sample': instance.typeOfSample,
      'sample_details': instance.sampleDetails,
      'sample_code': instance.sampleCode,
      'samples': instance.samples,
      'deleted': instance.deleted,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
