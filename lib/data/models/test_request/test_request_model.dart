import 'package:json_annotation/json_annotation.dart';
import 'sample_model.dart';

part 'test_request_model.g.dart';

@JsonSerializable()
class TestRequestModel {
  final int id;
  @Json<PERSON><PERSON>(name: 'request_number')
  final String requestNumber;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'request_date')
  final String requestDate;
  @J<PERSON><PERSON><PERSON>(name: 'customer_name')
  final String customerName;
  @Json<PERSON><PERSON>(name: 'customer_address')
  final String customerAddress;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'contact_person')
  final String contactPerson;
  @Json<PERSON><PERSON>(name: 'mobile_no')
  final String mobileNo;
  final String email;
  @Json<PERSON><PERSON>(name: 'special_request')
  final String? specialRequest;
  @J<PERSON><PERSON><PERSON>(name: 'submitted_by_name')
  final String submittedByName;
  @<PERSON>son<PERSON><PERSON>(name: 'submitted_by_designation')
  final String submittedByDesignation;
  @<PERSON>son<PERSON>ey(name: 'submitted_by_date')
  final String submittedByDate;
  @JsonKey(name: 'submitted_by_id_proof')
  final String submittedByIdProof;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'received_by_name')
  final String receivedByName;
  @J<PERSON><PERSON><PERSON>(name: 'received_by_designation')
  final String receivedByDesignation;
  @<PERSON>son<PERSON><PERSON>(name: 'received_by_date')
  final String receivedByDate;
  @JsonKey(name: 'received_by_id_proof')
  final String receivedByIdProof;
  @JsonKey(name: 'sample_received_time')
  final String sampleReceivedTime;
  @JsonKey(name: 'sample_collection_time')
  final String sampleCollectionTime;
  @JsonKey(name: 'quantity_of_sample')
  final String quantityOfSample;
  @JsonKey(name: 'type_of_sample')
  final String? typeOfSample;
  @JsonKey(name: 'sample_details')
  final String? sampleDetails;
  @JsonKey(name: 'sample_code')
  final String? sampleCode;
  final List<SampleModel>? samples;
  final int deleted;
  @JsonKey(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  const TestRequestModel({
    required this.id,
    required this.requestNumber,
    required this.requestDate,
    required this.customerName,
    required this.customerAddress,
    required this.contactPerson,
    required this.mobileNo,
    required this.email,
    this.specialRequest,
    required this.submittedByName,
    required this.submittedByDesignation,
    required this.submittedByDate,
    required this.submittedByIdProof,
    required this.receivedByName,
    required this.receivedByDesignation,
    required this.receivedByDate,
    required this.receivedByIdProof,
    required this.sampleReceivedTime,
    required this.sampleCollectionTime,
    required this.quantityOfSample,
    this.typeOfSample,
    this.sampleDetails,
    this.sampleCode,
    this.samples,
    this.deleted = 0,
    this.createdAt,
    this.updatedAt,
  });

  factory TestRequestModel.fromJson(Map<String, dynamic> json) =>
      _$TestRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$TestRequestModelToJson(this);
}
