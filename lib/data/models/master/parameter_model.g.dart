// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parameter_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ParameterModel _$ParameterModelFromJson(Map<String, dynamic> json) =>
    ParameterModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      type: json['type'] as String?,
      status: (json['status'] as num).toInt(),
      requirement: json['requirement'] as String?,
      permissibleLimit: json['permissible_limit'] as String?,
      protocolUsed: json['protocol_used'] as String?,
      units: json['units'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$ParameterModelToJson(ParameterModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'status': instance.status,
      'requirement': instance.requirement,
      'permissible_limit': instance.permissibleLimit,
      'protocol_used': instance.protocolUsed,
      'units': instance.units,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
