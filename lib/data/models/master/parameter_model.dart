import 'package:json_annotation/json_annotation.dart';

part 'parameter_model.g.dart';

@JsonSerializable()
class ParameterModel {
  final int id;
  final String name;
  final String? type;
  final int status;
  final String? requirement;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'permissible_limit')
  final String? permissibleLimit;
  @J<PERSON><PERSON><PERSON>(name: 'protocol_used')
  final String? protocolUsed;
  final String? units;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  const ParameterModel({
    required this.id,
    required this.name,
    this.type,
    required this.status,
    this.requirement,
    this.permissibleLimit,
    this.protocolUsed,
    this.units,
    this.createdAt,
    this.updatedAt,
  });

  factory ParameterModel.fromJson(Map<String, dynamic> json) =>
      _$ParameterModelFromJson(json);

  Map<String, dynamic> toJson() => _$ParameterModelToJson(this);
}
