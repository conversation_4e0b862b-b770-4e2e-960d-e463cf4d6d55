import 'package:dartz/dartz.dart';
import 'package:lims_app_flutter/domain/entities/common/pagination_entity.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/job_allocation/job_allocation_entity.dart';
import '../../domain/repositories/job_allocation_repository.dart';
import '../datasources/job_allocation_remote_data_source.dart';
import '../models/job_allocation/job_allocation_model.dart';

class JobAllocationRepositoryImpl implements JobAllocationRepository {
  final JobAllocationRemoteDataSource _remoteDataSource;

  JobAllocationRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, List<JobAllocationEntity>>> getJobAllocations({
    int page = 1,
    int perPage = 15,
    String? search,
  }) async {
    try {
      final result = await _remoteDataSource.getJobAllocations( page: page,
        perPage: perPage,
        search: search,);
      final entities = result.map(_mapJobAllocationModelToEntity).toList();
      final paginationEntity = PaginationEntity(
        currentPage: result.pagination.currentPage,
        totalPages: result.pagination.totalPages,
        perPage: result.pagination.perPage,
        total: result.pagination.total,
        hasMore: result.pagination.hasMore,
      );

      final paginatedResult = PaginatedResult<TestRequestEntity>(
        items: entities,
        pagination: paginationEntity,
      );
      return Right(paginatedResult);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, JobAllocationEntity>> getJobAllocationById(int id) async {
    try {
      final result = await _remoteDataSource.getJobAllocationById(id);
      final entity = _mapJobAllocationModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, JobAllocationEntity>> createJobAllocation(
    JobAllocationEntity jobAllocation,
  ) async {
    try {
      final model = _mapJobAllocationEntityToModel(jobAllocation);
      final result = await _remoteDataSource.createJobAllocation(model);
      final entity = _mapJobAllocationModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, JobAllocationEntity>> updateJobAllocation(
    int id,
    JobAllocationEntity jobAllocation,
  ) async {
    try {
      final model = _mapJobAllocationEntityToModel(jobAllocation);
      final result = await _remoteDataSource.updateJobAllocation(id, model);
      final entity = _mapJobAllocationModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteJobAllocation(int id) async {
    try {
      await _remoteDataSource.deleteJobAllocation(id);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  // Helper methods for mapping between models and entities
  JobAllocationEntity _mapJobAllocationModelToEntity(JobAllocationModel model) {
    return JobAllocationEntity(
      id: model.id,
      serialNo: model.serialNo,
      creationDate: DateTime.parse(model.creationDate),
      codeNumber: model.codeNumber,
      nature: model.nature,
      quantity: model.quantity,
      collectionDate: DateTime.parse(model.collectionDate),
      submissionDate: DateTime.parse(model.submissionDate),
      dueDate: DateTime.parse(model.dueDate),
      userId: model.userId,
      testRequestId: model.testRequestId,
      reportType: model.reportType,
      designation: model.designation,
      remarks: model.remarks,
      nablStatus: model.nablStatus,
      createdAt: model.createdAt != null ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null ? DateTime.tryParse(model.updatedAt!) : null,
    );
  }

  JobAllocationModel _mapJobAllocationEntityToModel(JobAllocationEntity entity) {
    return JobAllocationModel(
      id: entity.id,
      serialNo: entity.serialNo,
      creationDate: entity.creationDate.toIso8601String(),
      codeNumber: entity.codeNumber,
      nature: entity.nature,
      quantity: entity.quantity,
      collectionDate: entity.collectionDate.toIso8601String().split('T')[0],
      submissionDate: entity.submissionDate.toIso8601String().split('T')[0],
      dueDate: entity.dueDate.toIso8601String().split('T')[0],
      userId: entity.userId,
      testRequestId: entity.testRequestId,
      reportType: entity.reportType,
      designation: entity.designation,
      remarks: entity.remarks,
      nablStatus: entity.nablStatus,
      createdAt: entity.createdAt?.toIso8601String(),
      updatedAt: entity.updatedAt?.toIso8601String(),
    );
  }
}
